import _slicedToArray from "@babel/runtime/helpers/esm/slicedToArray";
import * as React from 'react';
import useEvent from "./useEvent";
/**
 * Same as React.useState but will always get latest state.
 * This is useful when React merge multiple state updates into one.
 * e.g. onTransitionEnd trigger multiple event at once will be merged state update in React.
 */
export default function useSyncState(defaultValue) {
  var _React$useReducer = React.useReducer(function (x) {
      return x + 1;
    }, 0),
    _React$useReducer2 = _slicedToArray(_React$useReducer, 2),
    forceUpdate = _React$useReducer2[1];
  var currentValueRef = React.useRef(defaultValue);
  var getValue = useEvent(function () {
    return currentValueRef.current;
  });
  var setValue = useEvent(function (updater) {
    currentValueRef.current = typeof updater === 'function' ? updater(currentValueRef.current) : updater;
    forceUpdate();
  });
  return [getValue, setValue];
}